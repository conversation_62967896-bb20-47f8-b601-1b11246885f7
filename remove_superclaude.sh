#!/bin/bash

# SuperClaude Framework Removal Script
# This script safely removes the SuperClaude Framework from Claude Code
# while preserving MCP servers and other configurations

echo "=========================================="
echo "SuperClaude Framework Removal Script"
echo "=========================================="
echo ""

# Define the Claude configuration directory
CLAUDE_DIR="$HOME/.claude"

# Define SuperClaude Framework files to remove
SUPERCLAUDE_FILES=(
    "CLAUDE.md"
    "COMMANDS.md"
    "FLAGS.md"
    "PRINCIPLES.md"
    "RULES.md"
    "MCP.md"
    "PERSONAS.md"
    "ORCHESTRATOR.md"
    "MODES.md"
)

# Create backup directory with timestamp
BACKUP_DIR="$HOME/.claude_superclaude_backup_$(date +%Y%m%d_%H%M%S)"

echo "Step 1: Creating backup directory..."
echo "Backup location: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Backup existing SuperClaude files
echo ""
echo "Step 2: Backing up SuperClaude files (if they exist)..."
FILES_BACKED_UP=0
for file in "${SUPERCLAUDE_FILES[@]}"; do
    if [ -f "$CLAUDE_DIR/$file" ]; then
        cp "$CLAUDE_DIR/$file" "$BACKUP_DIR/"
        echo "  ✓ Backed up: $file"
        FILES_BACKED_UP=$((FILES_BACKED_UP + 1))
    fi
done

if [ $FILES_BACKED_UP -eq 0 ]; then
    echo "  No SuperClaude files found to backup."
    rmdir "$BACKUP_DIR" 2>/dev/null
else
    echo "  Total files backed up: $FILES_BACKED_UP"
fi

# Remove SuperClaude files
echo ""
echo "Step 3: Removing SuperClaude Framework files..."
FILES_REMOVED=0
for file in "${SUPERCLAUDE_FILES[@]}"; do
    if [ -f "$CLAUDE_DIR/$file" ]; then
        rm "$CLAUDE_DIR/$file"
        echo "  ✓ Removed: $file"
        FILES_REMOVED=$((FILES_REMOVED + 1))
    else
        echo "  ⊘ Not found: $file (skipping)"
    fi
done

echo ""
echo "=========================================="
echo "Summary:"
echo "=========================================="
echo "Files removed: $FILES_REMOVED"
if [ $FILES_BACKED_UP -gt 0 ]; then
    echo "Files backed up: $FILES_BACKED_UP"
    echo "Backup location: $BACKUP_DIR"
    echo ""
    echo "To restore SuperClaude later, run:"
    echo "  cp $BACKUP_DIR/*.md $CLAUDE_DIR/"
fi

echo ""
echo "✅ SuperClaude Framework has been removed."
echo "Claude Code will now use its default behavior."
echo ""
echo "Note: MCP servers and other configurations have been preserved."
echo "You may need to restart Claude Code for changes to take full effect."